import { CommonModule } from "@angular/common";
import { Component, Input, signal, SimpleChanges, ViewChild } from "@angular/core";
import { ActivatedRoute, Router, RouterModule } from "@angular/router";
import { SchemaMarkupService } from "@src/app/core/services/schema-markup.service";
import { BrowserService } from "@src/app/modules/core/service/browser.service";
import { TranslationService } from "@src/app/modules/core/service/translation.service";
import { metaTag } from "@src/app/shared/models/meta-tag.mode";
import { SlugPipe } from "@src/app/shared/pipes/slug.pipe";
import { CommonService } from "@src/app/shared/services/common.service";
import { MenuItem } from "primeng/api";
import { Observable, map, of, tap } from "rxjs";
import { MetaService } from "src/app/modules/core/service/meta.service";
import { CategoryMenuDTO, LookupDTO } from "src/app/shared/models/lookup.model";
import { NtranslatePipe } from "src/app/shared/pipes/ntranslate.pipe";
import { DeviceDetectionService } from "src/app/shared/services/device-detection.service";
import { LookupService } from "src/app/shared/services/lookup.service";
import { HeroSectionComponent } from "@src/app/modules/home/<USER>/hero-section/hero-section.component";
import { BannerModel, CategoryImage } from "@src/app/shared/models/common.model";
import { CategoryHeaderComponent } from "../category-header/category-header.component";

@Component({
  selector: "app-new-category-header",
  standalone: true,
  imports: [CommonModule, RouterModule, NtranslatePipe, HeroSectionComponent, CategoryHeaderComponent],
  templateUrl: "./new-category-header.component.html",
  styleUrl: "./new-category-header.component.scss",
  providers: [SlugPipe],
})
export class NewCategoryHeaderComponent {
  @Input() category: any;
  @Input() subCategoryId: number;
  banners$: Observable<BannerModel[]>;
  stickerImage$: Observable<CategoryImage>;

  subCategories$: Observable<LookupDTO[]>;
  subCategory!: LookupDTO;
  categoriesList: MenuItem[];
  selectedCategory: MenuItem;
  parentCategory: MenuItem;
  mobilebanner: string = "";
  desktopbanner: string = "";
  categoryPageTitle: string = "";
  categoryInfo!: CategoryMenuDTO;
  isRtl = false;
  isOriginal = signal(false);

  constructor(
    private _lookupService: LookupService,
    private router: Router,
    public device: DeviceDetectionService,
    private metaService: MetaService,
    private commonService: CommonService,
    private browserService: BrowserService,
    private sp: SlugPipe,
    private schema: SchemaMarkupService,
    private translation: TranslationService,
    private activatedRoute: ActivatedRoute,
    private dv: DeviceDetectionService
  ) {
    
  }

  ngOnInit(): void {
    if (this.browserService.isBrowser()) {
      this.isRtl = document.dir === "rtl";
    }
  }

  get bannerHasSliderImage$(): Observable<boolean> {
    return this.banners$.pipe(
      map(banners =>
        banners.some(b =>
          b.type?.includes(this.device.isMobile ? 'CategorySliderImageMobile' : 'CategorySliderImageWeb')
        )
      )
    );
  }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes["category"]) {
      this.subCategories$ = this._lookupService
        .getChildCategories(+this.category.id!)
        .pipe(
          map((e) => e.data),
          tap((res) => {
            if (this.subCategoryId && res.length > 0) {
              this.subCategory = res.find(
                (item) => item.id == this.subCategoryId
              )!;
            }
          })
        );

      this.categoryInfo = this.activatedRoute.snapshot.data["categoryResolver"];
      this.banners$ = this.commonService.getAllCategoryBanners(this.categoryInfo.id).pipe(
        tap(res => {
          this.stickerImage$ = of(res.stickerImage);
          console.log(res.stickerImage);
          if(true){
            this.isOriginal.set(true)
          }
        }),
        map(res => res.images.map((e, i) => ({ ...e , imageURL : e.url , index: i }))),
      );
      this.categoriesList = this._lookupService.convertToMenuItems([
        this.categoryInfo,
      ]);

      const { category, parent } = this._lookupService.findCategoryById(
        this.subCategoryId ? this.subCategoryId : this.category.id,
        this.categoriesList
      );

      this.selectedCategory = category;
      this.parentCategory = parent ?? category;

      if (this.category?.queryParams) {
        this.categoryPageTitle = this.category?.queryParams["name"];
      }

      console.log(this.category?.queryParams);

      let description = "",
        url = "";

      if (this.selectedCategory!.queryParams!["metaTag"]) {
        let targetTag = this.selectedCategory!.queryParams![
          "metaTag"
        ] as metaTag;
        const targetTitle =
          targetTag?.title!.length > 0 ? targetTag?.title : category.label;
        description =
          targetTag?.description!.length > 0
            ? targetTag?.description
            : category.label;
        url =
          this.commonService.getSiteURL() +
          "/category/" +
          (parent?.id ? parent?.id + "/" : "") +
          this.selectedCategory!.id +
          "/" +
          encodeURIComponent(this.sp.transform(category.label));

        this.metaService.set({
          title: targetTitle,
          description: description,
          keywords: targetTag?.keywords ?? [],
          image: "",
          url: url,
        });
      } else {
        const categoryTitle = this.translation.instant(
          this.selectedCategory!.queryParams!["pageTitle"] ??
            this.selectedCategory!.queryParams!["name"]
        );
        description = categoryTitle;
        url =
          this.commonService.getSiteURL() +
          "/category/" +
          (parent?.id ? parent?.id + "/" : "") +
          this.selectedCategory!.id +
          "/" +
          encodeURIComponent(this.sp.transform(category.label));
        this.metaService.set({
          title: categoryTitle,
          description: description,
          image: "",
          url: url,
        });
      }

      if (parent?.id) {
        this.schema.generateCategorySchema(
          category.label,
          description,
          url,
          category.id,
          parent
        );
      } else {
        this.schema.generateCategorySchema(
          category.label,
          description,
          url,
          category.id,
          null,
          category.items
        );
      }

      const images = this.parentCategory!.queryParams!["images"];
      for (let i = 0; i < images.length; i++) {
        if (images[i]["type"] == "CoverDesktop") {
          this.mobilebanner = images[i]["url"] + "?v=1.1.2";
        } else if (images[i]["type"] == "CoverMobile") {
          this.desktopbanner = images[i]["url"] + "?v=1.1.2";
        }
      }
    }
  }
}
