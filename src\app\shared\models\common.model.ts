export interface BannerModel {
  id?: number;
  bannerURL?: string;
  imageURL?: string;
  bannerType?: string;
  isActive?: boolean;
  name?: string;
  isDeleted?: boolean;
  mobileImageURL?: string;
  desktopImageURL?: string;
  index?: number;
  type?: string; 
}
export interface ResponsiveBannerModel {
  mobile?: BannerModel;
  desktop?: BannerModel;
  data?: BannerModel;
  index: number;
}
export interface CategoryImage {
  type: string;
  name: string;
  url: string;
}
export interface CategoryDetailsModel {
  id: number;
  name: string;
  images: CategoryImage[];
  stickerImage?: CategoryImage;
}

